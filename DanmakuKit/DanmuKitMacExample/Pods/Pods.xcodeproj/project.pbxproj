// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		254781A2F24C0071BA46C95B0DBD918C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9EE00D34B275026D4656C738BED5A001 /* Cocoa.framework */; };
		3B61F353BE6642A59B305D939D4863E8 /* DanmakuViewAdapter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A67C4495C4F4FE008BA3B34C0C4916DF /* DanmakuViewAdapter.swift */; };
		4F8C9386F0D4C025727F87E8ACB76C6F /* DanmakuQueuePool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 090FF6F5FA0ADD4A682DE7A9654D5E5D /* DanmakuQueuePool.swift */; };
		4FB33B3AAA1712BCAA65108F3C1FC3EF /* DanmakuAsyncLayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63A705CF3940517BAD9EB8EE59EDD7D0 /* DanmakuAsyncLayer.swift */; };
		52ABB0783C4528BD5A2D9DDEEBADBD0F /* DanmakuCellModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34199C52C2E993CAF2C68C19FE0D17D2 /* DanmakuCellModel.swift */; };
		5C6AB7691D505EBDA7DB71684C27E448 /* DanmakuPlatformCompatibility.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C11FD0BFDE56A820456E6838E8D18ED /* DanmakuPlatformCompatibility.swift */; };
		61FAAD71D6F5AFF85E0C5A78F181CC5E /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9EE00D34B275026D4656C738BED5A001 /* Cocoa.framework */; };
		76E48B6E5E5788DECCDAF0C0849E0215 /* DanmakuView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6038170BA1E9CE4C7219AD4144E14DCC /* DanmakuView.swift */; };
		796434D62FC0BE4715D7C0AEB022AB06 /* Pods-DanmuKitMacExample-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 7CA2087971D6395DA5214A9173D3B78C /* Pods-DanmuKitMacExample-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8271D3B5B0B571B6C37C703F44C6B512 /* DanmakuKit-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 0011F9EEE490A5D34AE4582A042CB1DA /* DanmakuKit-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		90E10F55A2775A707F45B11E2F9B94AA /* DanmakuGifCellModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F0E76D443002DB49E33B9107806DE83 /* DanmakuGifCellModel.swift */; };
		ADF061B75853D4786B97CE40BE8E5E1C /* DanmakuTrack.swift in Sources */ = {isa = PBXBuildFile; fileRef = A67B02FC17104C0A966F8490D76FB84B /* DanmakuTrack.swift */; };
		BD986DD1C26458688175A42F2D1D8276 /* GifAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 45C963EB8634232CA3C76C4057A46E87 /* GifAnimator.swift */; };
		E14C972E7D9AA6327B3ECB4D096FFCE8 /* DanmakuCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 930F964A5C029242FAB2154A8378DCAD /* DanmakuCell.swift */; };
		E3C77C8BC88D6608B1D6D4E53047D0DD /* DanmakuGifCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33A4C9E0CB7E41529C0BDC1E8B7AC5A4 /* DanmakuGifCell.swift */; };
		EA85C93A1143D1AA6AA2B08A00EE724B /* DanmakuKit-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = C0DAA555C677BB138D15A7367F9BE138 /* DanmakuKit-dummy.m */; };
		FE3C05B45B74C01E161221C61AB4689A /* Pods-DanmuKitMacExample-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BC1DB37F2F900F5D36D0CDBED2B5D1C /* Pods-DanmuKitMacExample-dummy.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		01F0D8F7A1FA70D70FA58F1E3DEFC87B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17053C38B0DBEC6E87321F38401435CD;
			remoteInfo = DanmakuKit;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0011F9EEE490A5D34AE4582A042CB1DA /* DanmakuKit-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "DanmakuKit-umbrella.h"; sourceTree = "<group>"; };
		0074C82DB6CC8C6AFCA2581C707CD26F /* Pods-DanmuKitMacExample-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-DanmuKitMacExample-acknowledgements.plist"; sourceTree = "<group>"; };
		090FF6F5FA0ADD4A682DE7A9654D5E5D /* DanmakuQueuePool.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuQueuePool.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuQueuePool.swift; sourceTree = "<group>"; };
		0D695D54315E0462110720A128C06C16 /* Pods-DanmuKitMacExample.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-DanmuKitMacExample.release.xcconfig"; sourceTree = "<group>"; };
		2F0E76D443002DB49E33B9107806DE83 /* DanmakuGifCellModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuGifCellModel.swift; path = Sources/DanmakuKit/Classes/Gif/DanmakuGifCellModel.swift; sourceTree = "<group>"; };
		33A4C9E0CB7E41529C0BDC1E8B7AC5A4 /* DanmakuGifCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuGifCell.swift; path = Sources/DanmakuKit/Classes/Gif/DanmakuGifCell.swift; sourceTree = "<group>"; };
		34199C52C2E993CAF2C68C19FE0D17D2 /* DanmakuCellModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuCellModel.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuCellModel.swift; sourceTree = "<group>"; };
		3A6087261FFFB743EDEF77DE9F497C04 /* DanmakuKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = DanmakuKit.release.xcconfig; sourceTree = "<group>"; };
		3BE1A7EC0A4EBA7639B205A4C5D2A326 /* Pods-DanmuKitMacExample-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-DanmuKitMacExample-acknowledgements.markdown"; sourceTree = "<group>"; };
		45C963EB8634232CA3C76C4057A46E87 /* GifAnimator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = GifAnimator.swift; path = Sources/DanmakuKit/Classes/Gif/GifAnimator.swift; sourceTree = "<group>"; };
		461C9AC50D0C098A8FD311CDDDEFDE78 /* DanmakuKit-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "DanmakuKit-prefix.pch"; sourceTree = "<group>"; };
		479B03EECF526B195DDD454F22AD9367 /* DanmakuKit.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = DanmakuKit.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		4B988B90236A26277BFFBE88C08577E1 /* Pods-DanmuKitMacExample */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-DanmuKitMacExample"; path = Pods_DanmuKitMacExample.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5198270EA722DA09ABDF40527E8B0273 /* DanmakuKit.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = DanmakuKit.modulemap; sourceTree = "<group>"; };
		597C6DA13E98C76A323AB891698AF6C7 /* Pods-DanmuKitMacExample.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-DanmuKitMacExample.modulemap"; sourceTree = "<group>"; };
		5BC1DB37F2F900F5D36D0CDBED2B5D1C /* Pods-DanmuKitMacExample-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-DanmuKitMacExample-dummy.m"; sourceTree = "<group>"; };
		6038170BA1E9CE4C7219AD4144E14DCC /* DanmakuView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuView.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuView.swift; sourceTree = "<group>"; };
		63A705CF3940517BAD9EB8EE59EDD7D0 /* DanmakuAsyncLayer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuAsyncLayer.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuAsyncLayer.swift; sourceTree = "<group>"; };
		67A117F959EB2B2443897F937FE5C1FA /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; path = LICENSE; sourceTree = "<group>"; };
		6E32BDC021E723C735E8D72919493597 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; path = README.md; sourceTree = "<group>"; };
		7CA2087971D6395DA5214A9173D3B78C /* Pods-DanmuKitMacExample-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-DanmuKitMacExample-umbrella.h"; sourceTree = "<group>"; };
		7F2AFB09883864594E73ADBB9F383BAF /* Pods-DanmuKitMacExample.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-DanmuKitMacExample.debug.xcconfig"; sourceTree = "<group>"; };
		81FD1C261C61694BCB79A3B7F88E04E9 /* DanmakuKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "DanmakuKit-Info.plist"; sourceTree = "<group>"; };
		8BC91C5788DEC19EF12A4F8C48DF2B14 /* Pods-DanmuKitMacExample-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-DanmuKitMacExample-frameworks.sh"; sourceTree = "<group>"; };
		8C11FD0BFDE56A820456E6838E8D18ED /* DanmakuPlatformCompatibility.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuPlatformCompatibility.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuPlatformCompatibility.swift; sourceTree = "<group>"; };
		930F964A5C029242FAB2154A8378DCAD /* DanmakuCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuCell.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuCell.swift; sourceTree = "<group>"; };
		96C5DE7304F29553B3AB0413C625DFA3 /* DanmakuKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = DanmakuKit.debug.xcconfig; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9EE00D34B275026D4656C738BED5A001 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/System/Library/Frameworks/Cocoa.framework; sourceTree = DEVELOPER_DIR; };
		A67B02FC17104C0A966F8490D76FB84B /* DanmakuTrack.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuTrack.swift; path = Sources/DanmakuKit/Classes/Core/DanmakuTrack.swift; sourceTree = "<group>"; };
		A67C4495C4F4FE008BA3B34C0C4916DF /* DanmakuViewAdapter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DanmakuViewAdapter.swift; path = Sources/DanmakuKit/Classes/SwiftUI/DanmakuViewAdapter.swift; sourceTree = "<group>"; };
		BDF4AB7FEAA2BB954A8AA25458D13ED2 /* DanmakuKit */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = DanmakuKit; path = DanmakuKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BF07FAD4B0B8469F250585929F713142 /* Pods-DanmuKitMacExample-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-DanmuKitMacExample-Info.plist"; sourceTree = "<group>"; };
		C0DAA555C677BB138D15A7367F9BE138 /* DanmakuKit-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "DanmakuKit-dummy.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		044B646AF2C07C0BFBCB11D565ACF59E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				61FAAD71D6F5AFF85E0C5A78F181CC5E /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		54BEAA1B5E25E423C951B86EA8B68E6C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				254781A2F24C0071BA46C95B0DBD918C /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		42AA83E95CEB8AEE911A9A4BECA09F01 /* DanmakuKit */ = {
			isa = PBXGroup;
			children = (
				63203E7248FD862E7B53FA4B2FF55054 /* Core */,
				7071AA869D16485C3BB713F2A27FB262 /* Gif */,
				465C0DEAD0BEFA256CFE37DAD36F9552 /* Pod */,
				F60922C12823446AE2566BD9A29514D0 /* Support Files */,
				44B7844AAAB97F491A40707509EBEF8F /* SwiftUI */,
			);
			name = DanmakuKit;
			path = ../..;
			sourceTree = "<group>";
		};
		44B7844AAAB97F491A40707509EBEF8F /* SwiftUI */ = {
			isa = PBXGroup;
			children = (
				A67C4495C4F4FE008BA3B34C0C4916DF /* DanmakuViewAdapter.swift */,
			);
			name = SwiftUI;
			sourceTree = "<group>";
		};
		464BA56AF62ED91C210FA18C41CE93C2 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				42AA83E95CEB8AEE911A9A4BECA09F01 /* DanmakuKit */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		465C0DEAD0BEFA256CFE37DAD36F9552 /* Pod */ = {
			isa = PBXGroup;
			children = (
				479B03EECF526B195DDD454F22AD9367 /* DanmakuKit.podspec */,
				67A117F959EB2B2443897F937FE5C1FA /* LICENSE */,
				6E32BDC021E723C735E8D72919493597 /* README.md */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		63203E7248FD862E7B53FA4B2FF55054 /* Core */ = {
			isa = PBXGroup;
			children = (
				63A705CF3940517BAD9EB8EE59EDD7D0 /* DanmakuAsyncLayer.swift */,
				930F964A5C029242FAB2154A8378DCAD /* DanmakuCell.swift */,
				34199C52C2E993CAF2C68C19FE0D17D2 /* DanmakuCellModel.swift */,
				8C11FD0BFDE56A820456E6838E8D18ED /* DanmakuPlatformCompatibility.swift */,
				090FF6F5FA0ADD4A682DE7A9654D5E5D /* DanmakuQueuePool.swift */,
				A67B02FC17104C0A966F8490D76FB84B /* DanmakuTrack.swift */,
				6038170BA1E9CE4C7219AD4144E14DCC /* DanmakuView.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		6CF8013CA832F8AB5DDAE0156C97FD88 /* OS X */ = {
			isa = PBXGroup;
			children = (
				9EE00D34B275026D4656C738BED5A001 /* Cocoa.framework */,
			);
			name = "OS X";
			sourceTree = "<group>";
		};
		7071AA869D16485C3BB713F2A27FB262 /* Gif */ = {
			isa = PBXGroup;
			children = (
				33A4C9E0CB7E41529C0BDC1E8B7AC5A4 /* DanmakuGifCell.swift */,
				2F0E76D443002DB49E33B9107806DE83 /* DanmakuGifCellModel.swift */,
				45C963EB8634232CA3C76C4057A46E87 /* GifAnimator.swift */,
			);
			name = Gif;
			sourceTree = "<group>";
		};
		7E0184B2C85BE3D6E7250B298F5B87B5 /* Pods-DanmuKitMacExample */ = {
			isa = PBXGroup;
			children = (
				597C6DA13E98C76A323AB891698AF6C7 /* Pods-DanmuKitMacExample.modulemap */,
				3BE1A7EC0A4EBA7639B205A4C5D2A326 /* Pods-DanmuKitMacExample-acknowledgements.markdown */,
				0074C82DB6CC8C6AFCA2581C707CD26F /* Pods-DanmuKitMacExample-acknowledgements.plist */,
				5BC1DB37F2F900F5D36D0CDBED2B5D1C /* Pods-DanmuKitMacExample-dummy.m */,
				8BC91C5788DEC19EF12A4F8C48DF2B14 /* Pods-DanmuKitMacExample-frameworks.sh */,
				BF07FAD4B0B8469F250585929F713142 /* Pods-DanmuKitMacExample-Info.plist */,
				7CA2087971D6395DA5214A9173D3B78C /* Pods-DanmuKitMacExample-umbrella.h */,
				7F2AFB09883864594E73ADBB9F383BAF /* Pods-DanmuKitMacExample.debug.xcconfig */,
				0D695D54315E0462110720A128C06C16 /* Pods-DanmuKitMacExample.release.xcconfig */,
			);
			name = "Pods-DanmuKitMacExample";
			path = "Target Support Files/Pods-DanmuKitMacExample";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				464BA56AF62ED91C210FA18C41CE93C2 /* Development Pods */,
				E0A1E60606E0BF6E2E10F1F01350DFE8 /* Frameworks */,
				FCFD31DB17A19E141A2241B3BA58BBFB /* Products */,
				F5C64915682096D26645DC3A1DE1D7D9 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		E0A1E60606E0BF6E2E10F1F01350DFE8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6CF8013CA832F8AB5DDAE0156C97FD88 /* OS X */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F5C64915682096D26645DC3A1DE1D7D9 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				7E0184B2C85BE3D6E7250B298F5B87B5 /* Pods-DanmuKitMacExample */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		F60922C12823446AE2566BD9A29514D0 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				5198270EA722DA09ABDF40527E8B0273 /* DanmakuKit.modulemap */,
				C0DAA555C677BB138D15A7367F9BE138 /* DanmakuKit-dummy.m */,
				81FD1C261C61694BCB79A3B7F88E04E9 /* DanmakuKit-Info.plist */,
				461C9AC50D0C098A8FD311CDDDEFDE78 /* DanmakuKit-prefix.pch */,
				0011F9EEE490A5D34AE4582A042CB1DA /* DanmakuKit-umbrella.h */,
				96C5DE7304F29553B3AB0413C625DFA3 /* DanmakuKit.debug.xcconfig */,
				3A6087261FFFB743EDEF77DE9F497C04 /* DanmakuKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "DanmuKitMacExample/Pods/Target Support Files/DanmakuKit";
			sourceTree = "<group>";
		};
		FCFD31DB17A19E141A2241B3BA58BBFB /* Products */ = {
			isa = PBXGroup;
			children = (
				BDF4AB7FEAA2BB954A8AA25458D13ED2 /* DanmakuKit */,
				4B988B90236A26277BFFBE88C08577E1 /* Pods-DanmuKitMacExample */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		062D2CE3A2D42FEBC4F3955D38CC2AA6 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8271D3B5B0B571B6C37C703F44C6B512 /* DanmakuKit-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BB42F9D5F0DA95060B8544A327D408FB /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				796434D62FC0BE4715D7C0AEB022AB06 /* Pods-DanmuKitMacExample-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		01252621E6865657E56C1993EA7B354C /* Pods-DanmuKitMacExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E158254BEFF01E1B193726DE5C7331DA /* Build configuration list for PBXNativeTarget "Pods-DanmuKitMacExample" */;
			buildPhases = (
				BB42F9D5F0DA95060B8544A327D408FB /* Headers */,
				4F525DA13B3E51CAC361847FDB25A9D2 /* Sources */,
				54BEAA1B5E25E423C951B86EA8B68E6C /* Frameworks */,
				5EE6109BD566DE46FC05ACFEF3F7CC1F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0E705282124A3BA3222AA6939E20CEF5 /* PBXTargetDependency */,
			);
			name = "Pods-DanmuKitMacExample";
			productName = Pods_DanmuKitMacExample;
			productReference = 4B988B90236A26277BFFBE88C08577E1 /* Pods-DanmuKitMacExample */;
			productType = "com.apple.product-type.framework";
		};
		17053C38B0DBEC6E87321F38401435CD /* DanmakuKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AE4D4E5A9082168C46AEDB27B73455DC /* Build configuration list for PBXNativeTarget "DanmakuKit" */;
			buildPhases = (
				062D2CE3A2D42FEBC4F3955D38CC2AA6 /* Headers */,
				36DEB5F048FFAD1B4CD83B2831275AB2 /* Sources */,
				044B646AF2C07C0BFBCB11D565ACF59E /* Frameworks */,
				2DA3B396DDF796FD3662B8A54ABF6E81 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DanmakuKit;
			productName = DanmakuKit;
			productReference = BDF4AB7FEAA2BB954A8AA25458D13ED2 /* DanmakuKit */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = FCFD31DB17A19E141A2241B3BA58BBFB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17053C38B0DBEC6E87321F38401435CD /* DanmakuKit */,
				01252621E6865657E56C1993EA7B354C /* Pods-DanmuKitMacExample */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2DA3B396DDF796FD3662B8A54ABF6E81 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5EE6109BD566DE46FC05ACFEF3F7CC1F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		36DEB5F048FFAD1B4CD83B2831275AB2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4FB33B3AAA1712BCAA65108F3C1FC3EF /* DanmakuAsyncLayer.swift in Sources */,
				E14C972E7D9AA6327B3ECB4D096FFCE8 /* DanmakuCell.swift in Sources */,
				52ABB0783C4528BD5A2D9DDEEBADBD0F /* DanmakuCellModel.swift in Sources */,
				E3C77C8BC88D6608B1D6D4E53047D0DD /* DanmakuGifCell.swift in Sources */,
				90E10F55A2775A707F45B11E2F9B94AA /* DanmakuGifCellModel.swift in Sources */,
				EA85C93A1143D1AA6AA2B08A00EE724B /* DanmakuKit-dummy.m in Sources */,
				5C6AB7691D505EBDA7DB71684C27E448 /* DanmakuPlatformCompatibility.swift in Sources */,
				4F8C9386F0D4C025727F87E8ACB76C6F /* DanmakuQueuePool.swift in Sources */,
				ADF061B75853D4786B97CE40BE8E5E1C /* DanmakuTrack.swift in Sources */,
				76E48B6E5E5788DECCDAF0C0849E0215 /* DanmakuView.swift in Sources */,
				3B61F353BE6642A59B305D939D4863E8 /* DanmakuViewAdapter.swift in Sources */,
				BD986DD1C26458688175A42F2D1D8276 /* GifAnimator.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4F525DA13B3E51CAC361847FDB25A9D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FE3C05B45B74C01E161221C61AB4689A /* Pods-DanmuKitMacExample-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0E705282124A3BA3222AA6939E20CEF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DanmakuKit;
			target = 17053C38B0DBEC6E87321F38401435CD /* DanmakuKit */;
			targetProxy = 01F0D8F7A1FA70D70FA58F1E3DEFC87B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		018A407E49DBFC8C41DB39D857FFA915 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7F2AFB09883864594E73ADBB9F383BAF /* Pods-DanmuKitMacExample.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MODULEMAP_FILE = "Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		14564F69AD0AFF5DC5E0F7172A018E17 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		242193D4669DFA094486F31DB23C5A47 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3A6087261FFFB743EDEF77DE9F497C04 /* DanmakuKit.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DanmakuKit/DanmakuKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DanmakuKit/DanmakuKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MODULEMAP_FILE = "Target Support Files/DanmakuKit/DanmakuKit.modulemap";
				PRODUCT_MODULE_NAME = DanmakuKit;
				PRODUCT_NAME = DanmakuKit;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		60B2596ECDD6C71F410916EBD1BB7466 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0D695D54315E0462110720A128C06C16 /* Pods-DanmuKitMacExample.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MODULEMAP_FILE = "Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		918D54742EB9F9EF480FA12568D5A39B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		A28CDBEBE40053BECDD0E68E10648D7F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 96C5DE7304F29553B3AB0413C625DFA3 /* DanmakuKit.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/DanmakuKit/DanmakuKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/DanmakuKit/DanmakuKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MODULEMAP_FILE = "Target Support Files/DanmakuKit/DanmakuKit.modulemap";
				PRODUCT_MODULE_NAME = DanmakuKit;
				PRODUCT_NAME = DanmakuKit;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				14564F69AD0AFF5DC5E0F7172A018E17 /* Debug */,
				918D54742EB9F9EF480FA12568D5A39B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AE4D4E5A9082168C46AEDB27B73455DC /* Build configuration list for PBXNativeTarget "DanmakuKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A28CDBEBE40053BECDD0E68E10648D7F /* Debug */,
				242193D4669DFA094486F31DB23C5A47 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E158254BEFF01E1B193726DE5C7331DA /* Build configuration list for PBXNativeTarget "Pods-DanmuKitMacExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				018A407E49DBFC8C41DB39D857FFA915 /* Debug */,
				60B2596ECDD6C71F410916EBD1BB7466 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
