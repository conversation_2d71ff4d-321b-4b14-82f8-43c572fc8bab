//
//  DanmakuGifCellModel.swift
//  DanmakuKit
//
//  Created by <PERSON> on 2021/8/31.
//

import Foundation

public protocol DanmakuGifCellModel: DanmakuCellModel {
    
    /// GIF data source
    var resource: Data? { get }
    
    /// The animation duration of each frame, default is 0.1.
    var minFrameDuration: Float { get }
    
    /// Number of preloaded frames, default is 10.
    var preloadFrameCount: Int { get }
    
    /// Maximum number of repetitions of animation.
    var maxRepeatCount: Int { get }
    
    /// Decode image in background, default is true.
    var backgroundDecode: Bool { get }
    
}

public extension DanmakuGifCellModel {
    
    var minFrameDuration: Float {
        return 0.1
    }
    
    var preloadFrameCount: Int {
        return 10
    }
    
    var maxRepeatCount: Int {
        return .max
    }
    
    var backgroundDecode: Bool {
        return true
    }

}

#if os(macOS)
/// macOS 具体实现（遵循与 iOS 兼容的 DanmakuGifCellModel 协议）
public class DanmakuGifCellModelImpl: DanmakuGifCellModel {

    public var identifier: String = ""

    /// GIF 数据（二进制），与 iOS 一致的字段名
    public var resource: Data?

    /// 兼容旧 macOS 示例代码的别名（读写同 resource）
    public var gifData: Data? {
        get { resource }
        set { resource = newValue }
    }

    public var size: CGSize = .zero

    public var track: UInt?

    /// 弹幕整体显示时长（秒）
    public var displayTime: Double = 8.0

    public var type: DanmakuCellType = .floating

    public var cellClass: DanmakuCell.Type { DanmakuGifCell.self }

    public init() {}

    public func isEqual(to cellModel: DanmakuCellModel) -> Bool { identifier == cellModel.identifier }

    public func calculateSize() {
        guard let data = resource,
              let imageSource = CGImageSourceCreateWithData(data as CFData, nil),
              CGImageSourceGetCount(imageSource) > 0,
              let firstImage = CGImageSourceCreateImageAtIndex(imageSource, 0, nil) else {
            size = CGSize(width: 100, height: 100)
            return
        }
        size = CGSize(width: firstImage.width, height: firstImage.height)
    }
}
#endif
