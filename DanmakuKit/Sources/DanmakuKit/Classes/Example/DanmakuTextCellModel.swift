//
//  DanmakuTextCellModel.swift
//  DanmakuKit
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku model for both iOS and macOS
//

#if os(iOS) || os(tvOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Example text danmaku model that works on both iOS and macOS
public class DanmakuTextCellModel: DanmakuCellModel {
    
    public var identifier: String = ""
    
    public var text: String = ""
    
    #if os(iOS) || os(tvOS)
    public var font: UIFont = UIFont.systemFont(ofSize: 15)
    
    /// 文本颜色（默认白色）
    public var textColor: UIColor = UIColor.white

    /// 可配置描边（默认关闭：width/opactiy = 0）
    public var strokeColor: UIColor = UIColor.black
    public var strokeWidth: CGFloat = 0
    public var strokeOpacity: CGFloat = 0

    /// 可配置阴影（默认关闭：opacity/blur = 0）
    public var shadowColor: UIColor = UIColor.black
    public var shadowOpacity: CGFloat = 0
    public var shadowBlur: CGFloat = 0
    public var shadowOffset: CGSize = CGSize(width: 1, height: 1)
    #elseif os(macOS)
    public var font: NSFont = NSFont.systemFont(ofSize: 15)
    
    /// 文本颜色（默认白色）
    public var textColor: NSColor = NSColor.white

    /// 可配置描边（默认关闭：width/opactiy = 0）
    public var strokeColor: NSColor = NSColor.black
    public var strokeWidth: CGFloat = 0
    public var strokeOpacity: CGFloat = 0

    /// 可配置阴影（默认关闭：opacity/blur = 0）
    public var shadowColor: NSColor = NSColor.black
    public var shadowOpacity: CGFloat = 0
    public var shadowBlur: CGFloat = 0
    public var shadowOffset: CGSize = CGSize(width: 1, height: 1)
    #endif
    
    public var size: CGSize = .zero
    
    public var track: UInt?
    
    public var displayTime: Double = 8.0
    
    public var type: DanmakuCellType = .floating
    
    public var cellClass: DanmakuCell.Type {
        return DanmakuTextCell.self
    }
    
    public init() {}
    
    public init(text: String) {
        self.text = text
        self.identifier = UUID().uuidString
        calculateSize()
    }
    
    public func calculateSize() {
        // 计算文本尺寸，必要时考虑描边和阴影带来的额外边距
        #if os(iOS) || os(tvOS)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font
        ]

        let attributedString = NSAttributedString(string: text, attributes: attributes)
        let boundingRect = attributedString.boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            context: nil
        )

        let base = CGSize(width: ceil(boundingRect.width), height: ceil(boundingRect.height))
        #elseif os(macOS)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font
        ]

        let attributedString = NSAttributedString(string: text, attributes: attributes)
        let boundingRect = attributedString.boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading]
        )

        let base = CGSize(width: ceil(boundingRect.width), height: ceil(boundingRect.height))
        #endif

        // 只有当外界开启这些效果（>0）时，才添加额外 padding
        let strokeInset = max(0, strokeWidth)
        let shadowPadX = shadowOpacity > 0 ? (abs(shadowOffset.width) + shadowBlur) : 0
        let shadowPadY = shadowOpacity > 0 ? (abs(shadowOffset.height) + shadowBlur) : 0
        size = CGSize(width: base.width + strokeInset * 2 + shadowPadX,
                      height: base.height + strokeInset * 2 + shadowPadY)
    }
    
    public func isEqual(to cellModel: DanmakuCellModel) -> Bool {
        return identifier == cellModel.identifier
    }
    
}
