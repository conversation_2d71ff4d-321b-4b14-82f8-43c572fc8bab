//
//  DanmakuViewAdapter.swift
//  DanmakuKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/1/31.
//

import SwiftUI

#if os(iOS) || os(tvOS)
@available(iOS 14.0, tvOS 14.0, *)
#elseif os(macOS)
@available(macOS 10.15, *)
#endif
public struct DanmakuViewAdapter: DanmakuViewRepresentable {

    #if os(iOS) || os(tvOS)
    public typealias UIViewType = DanmakuView
    #elseif os(macOS)
    public typealias NSViewType = DanmakuView
    #endif
    
    @ObservedObject var coordinator: Coordinator
    
    public init(coordinator: Coordinator) {
        self.coordinator = coordinator
    }

    #if os(iOS) || os(tvOS)
    public func makeUIView(context: Context) -> UIViewType {
        return coordinator.makeView()
    }

    public func updateUIView(_ uiView: UIViewType, context: Context) {}
    #elseif os(macOS)
    public func makeNSView(context: Context) -> NSViewType {
        return coordinator.makeView()
    }

    public func updateNSView(_ nsView: NSViewType, context: Context) {}
    #endif

    public func makeCoordinator() -> Coordinator {
        return coordinator
    }
    
    public class Coordinator: ObservableObject {
        
        public init() {}
        
        public private(set) var danmakuView: DanmakuView?
        
        private var frameObserver: Any?
        
        public weak var danmakuViewDelegate: DanmakuViewDelegate? {
            willSet {
                danmakuView?.delegate = newValue
            }
        }
        
        public func play() {
            danmakuView?.play()
        }
        
        public func pause() {
            danmakuView?.pause()
        }
        
        public func stop() {
            danmakuView?.stop()
        }
        
        public func clean() {
            danmakuView?.clean()
        }
        
        public func shoot(danmaku: DanmakuCellModel) {
            danmakuView?.shoot(danmaku: danmaku)
        }
        
        public func canShoot(danmaku: DanmakuCellModel) -> Bool {
            guard let view = danmakuView else { return false }
            return view.canShoot(danmaku: danmaku)
        }
        
        public func recalculateTracks() {
            danmakuView?.recalculateTracks()
        }
        
        public func sync(danmaku: DanmakuCellModel, at progress: Float) {
            danmakuView?.sync(danmaku: danmaku, at: progress)
        }
        
        func makeView() -> DanmakuView {
            danmakuView = DanmakuView(frame: .zero)
            #if os(iOS) || os(tvOS)
            frameObserver = danmakuView?.publisher(for: \.frame).sink { [weak self] _ in
                guard let self = self else { return }
                self.danmakuView?.recalculateTracks()
            }
            #elseif os(macOS)
            // Note: NSView doesn't have a publisher for frame changes like UIView
            // We could use KVO or other observation methods if needed
            #endif
            return danmakuView!
        }
        
    }
    
}
