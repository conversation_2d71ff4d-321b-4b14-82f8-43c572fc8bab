//
//  DanmakuGifCell.swift
//  DanmakuKit
//
//  Created by <PERSON> on 2021/8/30.
//

#if os(iOS) || os(tvOS)
import UIKit
import MobileCoreServices
#elseif os(macOS)
import Cocoa
#endif

/// You can use or inherit this cell to shoot a danmaku with a GIF animation.
/// You need to implement the DanmakuGifCellModel protocol for your data source.
/// And specify that the cellClass generated by the data source is DanmakuGifCell or a subclass derived from it.
/// This is a subclass that only shows GIF capabilities.
/// If you want to implement other specific features, you can refer to the implementation of this class.
open class DanmakuGifCell: DanmakuCell {

    #if os(iOS) || os(tvOS)
    private var gifModel: DanmakuGifCellModel? {
        return model as? DanmakuGifCellModel
    }

    public private(set) var animator: GifAnimator?

    public required init(frame: CGRect) {
        super.init(frame: frame)
        displayAsync = false
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    #elseif os(macOS)
    private var gifAnimator: GifAnimator?
    private var currentImage: CGImage?

    public required override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        displayAsync = false
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    #endif

    open override func enterTrack() {
        super.enterTrack()
        #if os(iOS) || os(tvOS)
        prepare()
        animator?.startAnimation()
        #elseif os(macOS)
        setupGifAnimation()
        gifAnimator?.startAnimation()
        #endif
    }

    open override func leaveTrack() {
        super.leaveTrack()
        #if os(iOS) || os(tvOS)
        animator?.stopAnimation()
        animator = nil
        #elseif os(macOS)
        gifAnimator?.stopAnimation()
        gifAnimator = nil
        #endif
    }

    #if os(macOS)
    public override func willDisplay() {
        super.willDisplay()
        setupGifAnimation()
    }

    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled else { return }

        if let image = currentImage {
            context.draw(image, in: CGRect(origin: .zero, size: size))
        }
    }

    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
        if finished {
            gifAnimator?.startAnimation()
        }
    }

    private func setupGifAnimation() {
        // Support both protocol-conforming models and the previous class name for back-compat
        if let model = model as? DanmakuGifCellModel, let data = model.resource {
            gifAnimator = GifAnimator(data: data)
        } else {
            return
        }
        gifAnimator?.prepare()
        currentImage = gifAnimator?.currentFrameImage

        gifAnimator?.update = { [weak self] image in
            guard let self = self else { return }
            self.currentImage = image
            self.redraw()
        }
    }

    deinit {
        gifAnimator?.stopAnimation()
    }
    #endif

    #if os(iOS) || os(tvOS)
    private func prepare() {
        animator = nil
        guard let gifModel = gifModel else { return }
        guard let data = gifModel.resource else { return }
        guard let image = UIImage(data: data) else {
            debugPrint("Could not create gif animetion because image create failed.")
            return
        }
        
        let info: [CFString: Any] = [
            kCGImageSourceShouldCache: true,
            kCGImageSourceTypeIdentifierHint: kUTTypeGIF
        ]
        guard let imageSource = CGImageSourceCreateWithData(data as CFData, info as CFDictionary) else {
            debugPrint("Could not create gif animetion because imageSource create failed.")
            return
        }
        
        let animator = GifAnimator(imageSource: imageSource,
                                   preloadCount: gifModel.preloadFrameCount,
                                   imageSize: gifModel.size,
                                   imageScale: image.scale,
                                   maxRepeatCount: gifModel.maxRepeatCount)
        animator.backgroundDecode = gifModel.backgroundDecode
        animator.prepare()
        animator.update = { [weak self] in
            guard let frame = $0 else { return }
            self?.layer.contents = frame.cgImage
        }
        self.animator = animator
    }
    #endif

}

