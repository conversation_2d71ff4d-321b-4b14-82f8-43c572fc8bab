// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		71ED9D639AD4914D66AC1121 /* Pods_DanmuKitMacExample.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8FB4F34B9DA3CB3DACF8A657 /* Pods_DanmuKitMacExample.framework */; };
		8FEB61C42E59F07F00A67C3B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8FEB61BF2E59F07F00A67C3B /* Assets.xcassets */; };
		8FEB61C52E59F07F00A67C3B /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FEB61C02E59F07F00A67C3B /* ContentView.swift */; };
		8FEB61C62E59F07F00A67C3B /* DanmuKitMacExampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FEB61C22E59F07F00A67C3B /* DanmuKitMacExampleApp.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1F0370C13326E86B035A1A52 /* Pods-DanmuKitMacExample.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DanmuKitMacExample.debug.xcconfig"; path = "Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample.debug.xcconfig"; sourceTree = "<group>"; };
		616BD5D6176603F25B772067 /* Pods-DanmuKitMacExample.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-DanmuKitMacExample.release.xcconfig"; path = "Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample.release.xcconfig"; sourceTree = "<group>"; };
		8FB4F34B9DA3CB3DACF8A657 /* Pods_DanmuKitMacExample.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_DanmuKitMacExample.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8FEB61B02E59F04000A67C3B /* DanmuKitMacExample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DanmuKitMacExample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8FEB61BF2E59F07F00A67C3B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8FEB61C02E59F07F00A67C3B /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		8FEB61C12E59F07F00A67C3B /* DanmuKitMacExample.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = DanmuKitMacExample.entitlements; sourceTree = "<group>"; };
		8FEB61C22E59F07F00A67C3B /* DanmuKitMacExampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DanmuKitMacExampleApp.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8FEB61AD2E59F04000A67C3B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				71ED9D639AD4914D66AC1121 /* Pods_DanmuKitMacExample.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0CC6250BC766403AB8BD37CB /* Pods */ = {
			isa = PBXGroup;
			children = (
				1F0370C13326E86B035A1A52 /* Pods-DanmuKitMacExample.debug.xcconfig */,
				616BD5D6176603F25B772067 /* Pods-DanmuKitMacExample.release.xcconfig */,
			);
			name = Pods;
			path = Pods;
			sourceTree = "<group>";
		};
		8FEB61A72E59F04000A67C3B = {
			isa = PBXGroup;
			children = (
				8FEB61C32E59F07F00A67C3B /* DanmuKitMacExample */,
				8FEB61B12E59F04000A67C3B /* Products */,
				0CC6250BC766403AB8BD37CB /* Pods */,
				E2BBC1B48135FCE0325D371A /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8FEB61B12E59F04000A67C3B /* Products */ = {
			isa = PBXGroup;
			children = (
				8FEB61B02E59F04000A67C3B /* DanmuKitMacExample.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8FEB61C32E59F07F00A67C3B /* DanmuKitMacExample */ = {
			isa = PBXGroup;
			children = (
				8FEB61BF2E59F07F00A67C3B /* Assets.xcassets */,
				8FEB61C02E59F07F00A67C3B /* ContentView.swift */,
				8FEB61C12E59F07F00A67C3B /* DanmuKitMacExample.entitlements */,
				8FEB61C22E59F07F00A67C3B /* DanmuKitMacExampleApp.swift */,
			);
			path = DanmuKitMacExample;
			sourceTree = "<group>";
		};
		E2BBC1B48135FCE0325D371A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8FB4F34B9DA3CB3DACF8A657 /* Pods_DanmuKitMacExample.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8FEB61AF2E59F04000A67C3B /* DanmuKitMacExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8FEB61BC2E59F04100A67C3B /* Build configuration list for PBXNativeTarget "DanmuKitMacExample" */;
			buildPhases = (
				2ABF7E1915E23832B75ABD90 /* [CP] Check Pods Manifest.lock */,
				8FEB61AC2E59F04000A67C3B /* Sources */,
				8FEB61AD2E59F04000A67C3B /* Frameworks */,
				8FEB61AE2E59F04000A67C3B /* Resources */,
				4FD5FA8BD8B013AE5CD72FBD /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DanmuKitMacExample;
			productName = DanmuKitMacExample;
			productReference = 8FEB61B02E59F04000A67C3B /* DanmuKitMacExample.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8FEB61A82E59F04000A67C3B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					8FEB61AF2E59F04000A67C3B = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = 8FEB61AB2E59F04000A67C3B /* Build configuration list for PBXProject "DanmuKitMacExample" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8FEB61A72E59F04000A67C3B;
			productRefGroup = 8FEB61B12E59F04000A67C3B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8FEB61AF2E59F04000A67C3B /* DanmuKitMacExample */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8FEB61AE2E59F04000A67C3B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8FEB61C42E59F07F00A67C3B /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2ABF7E1915E23832B75ABD90 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-DanmuKitMacExample-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4FD5FA8BD8B013AE5CD72FBD /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-DanmuKitMacExample/Pods-DanmuKitMacExample-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8FEB61AC2E59F04000A67C3B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8FEB61C52E59F07F00A67C3B /* ContentView.swift in Sources */,
				8FEB61C62E59F07F00A67C3B /* DanmuKitMacExampleApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		8FEB61BA2E59F04100A67C3B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8FEB61BB2E59F04100A67C3B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8FEB61BD2E59F04100A67C3B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1F0370C13326E86B035A1A52 /* Pods-DanmuKitMacExample.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DanmuKitMacExample/DanmuKitMacExample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sail.DanmuKitMacExample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8FEB61BE2E59F04100A67C3B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 616BD5D6176603F25B772067 /* Pods-DanmuKitMacExample.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DanmuKitMacExample/DanmuKitMacExample.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sail.DanmuKitMacExample;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8FEB61AB2E59F04000A67C3B /* Build configuration list for PBXProject "DanmuKitMacExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8FEB61BA2E59F04100A67C3B /* Debug */,
				8FEB61BB2E59F04100A67C3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8FEB61BC2E59F04100A67C3B /* Build configuration list for PBXNativeTarget "DanmuKitMacExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8FEB61BD2E59F04100A67C3B /* Debug */,
				8FEB61BE2E59F04100A67C3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8FEB61A82E59F04000A67C3B /* Project object */;
}
