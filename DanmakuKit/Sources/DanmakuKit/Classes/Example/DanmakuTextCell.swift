//
//  DanmakuTextCell.swift
//  DanmakuKit
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku cell for both iOS and macOS
//

#if os(iOS) || os(tvOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Example text danmaku cell that works on both iOS and macOS
public class DanmakuTextCell: DanmakuCell {
    
    #if os(macOS)
    public override func willDisplay() {
        super.willDisplay()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled, let model = model as? DanmakuTextCellModel else { return }

        // 可配置的描边+阴影（默认都为 0，相当于纯文字，与 iOS 视觉一致）
        NSGraphicsContext.saveGraphicsState()
        let nsContext = NSGraphicsContext(cgContext: context, flipped: false)
        NSGraphicsContext.current = nsContext
        defer { NSGraphicsContext.restoreGraphicsState() }

        let text = NSString(string: model.text)

        let attributes: [NSAttributedString.Key: Any] = [
            .font: model.font,
            .foregroundColor: model.textColor
        ]

        // 计算文本绘制区域
        let textRect = text.boundingRect(with: size, options: [], attributes: attributes)
        let drawRect = CGRect(
            x: (size.width - textRect.width) / 2,
            y: (size.height - textRect.height) / 2,
            width: textRect.width,
            height: textRect.height
        )

        // 绘制文本
        text.draw(in: drawRect, withAttributes: attributes)
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
    }
    #endif
    
}
