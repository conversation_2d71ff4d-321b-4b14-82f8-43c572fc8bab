//
//  DanmakuView.swift
//  DanmakuKit
//
//  Created by <PERSON><PERSON> on 2020/8/16.
//

#if os(iOS) || os(tvOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

// Import platform compatibility layer
// 导入平台兼容性层

/// 弹幕视图代理协议 / Danmaku view delegate protocol
public protocol DanmakuViewDelegate: AnyObject {

    /// 将要复用弹幕 Cell（在调用前已为你设置好对应的模型） / A danmaku is about to be reused (cellModel is set before calling)
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 将要复用的弹幕 Cell / danmaku cell to be reused
    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell)

    /// 没有空间可以发射弹幕（不可重叠且所有轨道均无法容纳时触发） / No space to shoot danmaku (triggered when non-overlapping and all tracks are full)
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 尝试发射的弹幕模型 / danmaku model attempting to shoot
    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel)
    
    /// 即将显示弹幕（进入轨道前回调） / About to display danmaku (callback before entering track)
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 将要显示的弹幕 Cell / danmaku cell to be displayed
    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell)

    /// 弹幕显示结束（离开轨道后回调） / Danmaku display ended (callback after leaving track)
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 结束显示的弹幕 Cell / danmaku cell that ended displaying
    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell)

    /// 弹幕被点击时调用（macOS 专用） / Called when danmaku is clicked (macOS specific)
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 被点击的弹幕 Cell / clicked danmaku cell
    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell)

    /// 弹幕被轻点时调用（iOS/macOS 通用） / Called when danmaku is tapped (iOS/macOS universal)
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 被轻点的弹幕 Cell / tapped danmaku cell
    func danmakuView(_ danmakuView: DanmakuView, didTapped danmaku: DanmakuCell)

    /// 没有空间可以同步显示弹幕时调用 / Called when no space to sync display danmaku
    /// - Parameters:
    ///   - danmakuView: 弹幕视图 / danmaku view
    ///   - danmaku: 尝试同步的弹幕模型 / danmaku model attempting to sync
    func danmakuView(_ danmakuView: DanmakuView, noSpaceSync danmaku: DanmakuCellModel)
    
}

public extension DanmakuViewDelegate {

    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel) {}

    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, didTapped danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, noSpaceSync danmaku: DanmakuCellModel) {}

}

/// 弹幕播放状态 / Danmaku playback status
public enum DanmakuStatus {
    case play
    case pause
    case stop
}

/// 弹幕视图主类，用于显示和管理弹幕 / Main danmaku view class for displaying and managing danmaku
public class DanmakuView: DanmakuView_BaseView {

    /// 弹幕视图代理 / Danmaku view delegate
    public weak var delegate: DanmakuViewDelegate?

    /// 是否开启弹幕 Cell 复用（关闭后不再调用 delegate 的复用回调） / Enable danmaku cell reuse (delegate reuse callback won't be called if disabled)
    public var enableCellReusable = false
    
    /// 轨道高度（每条弹幕所在的轨道高度，影响可用轨道数量） / Track height (height of each danmaku track, affects number of available tracks)
    public var trackHeight: CGFloat = 20 {
        didSet {
            guard oldValue != trackHeight else { return }
            recalculateTracks()
        }
    }

    /// 顶部内边距（影响顶部弹幕的实际偏移） / Top padding (affects actual offset of top danmaku)
    public var paddingTop: CGFloat = 0 {
        didSet {
            guard oldValue != paddingTop else { return }
            recalculateTracks()
        }
    }

    /// 底部内边距（影响底部弹幕的实际偏移） / Bottom padding (affects actual offset of bottom danmaku)
    public var paddingBottom: CGFloat = 0 {
        didSet {
            guard oldValue != paddingBottom else { return }
            recalculateTracks()
        }
    }
    
    /// 显示区域占比 [0, 1]（影响可分配轨道的数量与垂直居中） / Display area ratio [0, 1] (affects number of assignable tracks and vertical centering)
    public var displayArea: CGFloat = 1.0 {
        willSet {
            assert(0 <= newValue && newValue <= 1, "Danmaku display area must be between [0, 1].")
        }
        didSet {
            guard oldValue != displayArea else { return }
            recalculateTracks()
        }
    }

    /// 是否允许弹幕重叠（true：可重叠；false：遵守防追尾逻辑） / Whether danmaku overlap is allowed (true: overlapping; false: follow anti-collision logic)
    public var isOverlap: Bool = false {
        didSet {
            for i in 0..<floatingTracks.count {
                floatingTracks[i].isOverlap = isOverlap
            }
            for i in 0..<topTracks.count {
                topTracks[i].isOverlap = isOverlap
            }
            for i in 0..<bottomTracks.count {
                bottomTracks[i].isOverlap = isOverlap
            }
        }
    }
    
    /// 是否启用"滚动弹幕"（漂浮弹幕） / Enable floating danmaku (scrolling danmaku)
    public var enableFloatingDanmaku: Bool = true {
        didSet {
            if !enableFloatingDanmaku {
                floatingTracks.forEach {
                    $0.stop()
                }
            }
        }
    }

    /// 是否启用"顶部固定弹幕" / Enable top fixed danmaku
    public var enableTopDanmaku: Bool = true {
        didSet {
            if !enableTopDanmaku {
                topTracks.forEach {
                    $0.stop()
                }
            }
        }
    }

    /// 是否启用"底部固定弹幕" / Enable bottom fixed danmaku
    public var enableBottomDanmaku: Bool = true {
        didSet {
            if !enableBottomDanmaku {
                bottomTracks.forEach {
                    $0.stop()
                }
            }
        }
    }
    
    /// 播放速度（影响所有轨道的动画速度） / Playing speed (affects animation speed of all tracks)
    public var playingSpeed: Float = 1.0 {
        willSet {
            assert(newValue > 0, "Danmaku playing speed must be over 0.")
        }
        didSet {
            update {
                for i in 0..<floatingTracks.count {
                    var track = floatingTracks[i]
                    track.playingSpeed = playingSpeed
                }
                for i in 0..<topTracks.count {
                    var track = topTracks[i]
                    track.playingSpeed = playingSpeed
                }
                for i in 0..<bottomTracks.count {
                    var track = bottomTracks[i]
                    track.playingSpeed = playingSpeed
                }
            }
        }
    }

    /// 当前播放状态 / Current playback status
    public private(set) var status: DanmakuStatus = .stop
    
    private var danmakuPool: [String: [DanmakuCell]] = [:]

    private var floatingTracks: [DanmakuTrack] = []

    private var topTracks: [DanmakuTrack] = []

    private var bottomTracks: [DanmakuTrack] = []

    private var viewHeight: CGFloat {
        return bounds.height * displayArea
    }

    #if os(macOS)
    /// macOS 特有：坐标系翻转 / macOS specific: coordinate system flipped
    public override var isFlipped: Bool { true }
    #endif

    #if os(iOS) || os(tvOS)
    public override init(frame: CGRect) {
        super.init(frame: frame)
        recalculateTracks()
    }
    #elseif os(macOS)
    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }
    #endif

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        #if os(macOS)
        setupView()
        #endif
    }

    deinit {
        stop()
    }

    #if os(macOS)
    private func setupView() {
        wantsLayer = true
        recalculateTracks()
    }
    #endif
    
    #if os(iOS) || os(tvOS)
    public override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        guard self.point(inside: point, with: event) else { return nil }

        for i in (0..<subviews.count).reversed() {
            let subView = subviews[i]
            var newPoint: CGPoint
            if subView.layer.animationKeys() != nil, let presentationLayer = subView.layer.presentation() {
                newPoint = layer.convert(point, to: presentationLayer)
            } else {
                newPoint = convert(point, to: subView)
            }
            if let findView = subView.hitTest(newPoint, with: event) {
                return findView
            }
        }
        return nil
    }
    #elseif os(macOS)
    public override func hitTest(_ point: NSPoint) -> NSView? {
        guard !isHidden, alphaValue > 0 else { return nil }
        guard self.bounds.contains(point) else { return nil }
        for sub in subviews.reversed() {
            var local = self.convert(point, to: sub)
            if let presentation = sub.layer?.presentation() {
                local = self.layer?.convert(point, to: presentation) ?? local
            }
            if let found = sub.hitTest(local) { return found }
        }
        return self
    }
    #endif

    #if os(macOS)
    public override func layout() {
        super.layout()
        recalculateTracks()
    }
    #endif

}

public extension DanmakuView {
    
    func shoot(danmaku: DanmakuCellModel) {
        guard status == .play else { return }
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return }
            guard !floatingTracks.isEmpty else { return }
        case .top:
            guard enableTopDanmaku else { return }
            guard !topTracks.isEmpty else { return }
        case .bottom:
            guard enableBottomDanmaku else { return }
            guard !bottomTracks.isEmpty else { return }
        }
        
        guard let cell = obtainCell(with: danmaku) else { return }
        
        let shootTrack: DanmakuTrack
        if isOverlap {
            shootTrack = findLeastNumberDanmakuTrack(for: danmaku)
        } else {
            guard let t = findSuitableTrack(for: danmaku) else {
                delegate?.danmakuView(self, noSpaceShoot: danmaku)
                if enableCellReusable {
                    appendCellToPool(cell)
                }
                return
            }
            shootTrack = t
        }
        
        if cell.superview == nil {
            addSubview(cell)
        }
        
        delegate?.danmakuView(self, willDisplay: cell)
        #if os(iOS) || os(tvOS)
        cell.layer.setNeedsDisplay()
        #elseif os(macOS)
        cell.layer?.setNeedsDisplay()
        #endif
        shootTrack.shoot(danmaku: cell)
    }
    
    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        guard status == .play else { return false }
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return false }
            return (floatingTracks.first { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) != nil
        case .top:
            guard enableTopDanmaku else { return false }
            return (topTracks.first { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) != nil
        case .bottom:
            guard enableBottomDanmaku else { return false }
            return (bottomTracks.first { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) != nil
        }
    }
    
    /// You can call this method when you need to change the size of the danmakuView.
    func recalculateTracks() {
        recalculateFloatingTracks()
        recalculateTopTracks()
        recalculateBottomTracks()
    }
    
    
    func play() {
        guard status != .play else { return }
        floatingTracks.forEach {
            $0.play()
        }
        topTracks.forEach {
            $0.play()
        }
        bottomTracks.forEach {
            $0.play()
        }
        status = .play
    }
    
    func pause() {
        guard status != .pause else { return }
        floatingTracks.forEach {
            $0.pause()
        }
        topTracks.forEach {
            $0.pause()
        }
        bottomTracks.forEach {
            $0.pause()
        }
        status = .pause
    }
    
    func stop() {
        guard status != .stop else { return }
        floatingTracks.forEach {
            $0.stop()
        }
        topTracks.forEach {
            $0.stop()
        }
        bottomTracks.forEach {
            $0.stop()
        }
        status = .stop
    }
    
    @discardableResult
    func play(_ danmaku: DanmakuCellModel) -> Bool {
        var track = floatingTracks.first { (t) -> Bool in
            return t.play(danmaku)
        }
        if track == nil {
            track = topTracks.first(where: { (t) -> Bool in
                return t.play(danmaku)
            })
        }
        if track == nil {
            track = bottomTracks.first(where: { (t) -> Bool in
                return t.play(danmaku)
            })
        }
        return track != nil
    }
    
    @discardableResult
    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        var track = floatingTracks.first { (t) -> Bool in
            return t.pause(danmaku)
        }
        if track == nil {
            track = topTracks.first(where: { (t) -> Bool in
                return t.pause(danmaku)
            })
        }
        if track == nil {
            track = bottomTracks.first(where: { (t) -> Bool in
                return t.pause(danmaku)
            })
        }
        return track != nil
    }
    
    /// Display a danmaku synchronously according to the progress. If the status is stop, it will not work.
    /// - Parameters:
    ///   - danmaku: danmakuCellModel
    ///   - progress: progress of danmaku display
    func sync(danmaku: DanmakuCellModel, at progress: Float) {
        guard status != .stop else { return }
        assert(progress <= 1.0, "Cannot sync danmaku at progress \(progress).")
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return }
            guard !floatingTracks.isEmpty else { return }
        case .top:
            guard enableTopDanmaku else { return }
            guard !topTracks.isEmpty else { return }
        case .bottom:
            guard enableBottomDanmaku else { return }
            guard !bottomTracks.isEmpty else { return }
        }
        guard let cell = obtainCell(with: danmaku) else { return }
        
        let syncTrack: DanmakuTrack
        if isOverlap {
            syncTrack = findLeastNumberDanmakuTrack(for: danmaku)
        } else {
            guard let t = findSuitableSyncTrack(for: danmaku, at: progress) else {
                delegate?.danmakuView(self, noSpaceSync: danmaku)
                return
            }
            syncTrack = t
        }
        
        if cell.superview == nil {
            addSubview(cell)
        }
        
        delegate?.danmakuView(self, willDisplay: cell)
        #if os(iOS) || os(tvOS)
        cell.layer.setNeedsDisplay()
        #elseif os(macOS)
        cell.layer?.setNeedsDisplay()
        #endif
        if status == .play {
            syncTrack.syncAndPlay(cell, at: progress)
        } else {
            syncTrack.sync(cell, at: progress)
        }
    }
    
    /// Clean all the currently displayed danmaku.
    func clean() {
        floatingTracks.forEach { $0.clean() }
        bottomTracks.forEach { $0.clean() }
        topTracks.forEach { $0.clean() }
    }
    
    /// When you change some properties of the danmakuView or cellModel that might affect the danmaku, you must make changes in the closure of this method.
    /// E.g.This method will be used when you change the displayTime property in the cellModel.
    /// - Parameter closure: update closure
    func update(_ closure: () -> Void) {
        pause()
        closure()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            self.play()
        }
    }
    
}

private extension DanmakuView {
    
    func recalculateFloatingTracks() {
        let trackCount = max(0, Int(floorf(Float((viewHeight - paddingTop - paddingBottom) / trackHeight))))
        let offsetY = max(0, (viewHeight - CGFloat(trackCount) * trackHeight) / 2.0)
        let diffFloatingTrackCount = trackCount - floatingTracks.count
        if diffFloatingTrackCount > 0 {
            for _ in 0..<diffFloatingTrackCount {
                floatingTracks.append(DanmakuFloatingTrack(view: self))
            }
        } else if diffFloatingTrackCount < 0 {
            for i in max(0, floatingTracks.count + diffFloatingTrackCount)..<floatingTracks.count {
                floatingTracks[i].stop()
            }
            floatingTracks.removeLast(Int(abs(diffFloatingTrackCount)))
        }
        for i in 0..<floatingTracks.count {
            var track = floatingTracks[i]
            track.stopClosure = { [weak self] (cell) in
                guard let strongSelf = self else { return }
                strongSelf.cellPlayingStop(cell)
            }
            track.index = UInt(i)
            track.positionY = CGFloat(i) * trackHeight + trackHeight / 2.0 + paddingTop + offsetY
        }
    }
    
    func recalculateTopTracks() {
        let trackCount = max(0, Int(floorf(Float((viewHeight - paddingTop - paddingBottom) / trackHeight))))
        let offsetY = max(0, (viewHeight - CGFloat(trackCount) * trackHeight) / 2.0)
        let diffFloatingTrackCount = trackCount - topTracks.count
        if diffFloatingTrackCount > 0 {
            for _ in 0..<diffFloatingTrackCount {
                topTracks.append(DanmakuVerticalTrack(view: self))
            }
        } else if diffFloatingTrackCount < 0 {
            for i in max(0, topTracks.count + diffFloatingTrackCount)..<topTracks.count {
                topTracks[i].stop()
            }
            topTracks.removeLast(Int(abs(diffFloatingTrackCount)))
        }
        for i in 0..<topTracks.count {
            var track = topTracks[i]
            track.stopClosure = { [weak self] (cell) in
                guard let strongSelf = self else { return }
                strongSelf.cellPlayingStop(cell)
            }
            track.index = UInt(i)
            track.positionY = CGFloat(i) * trackHeight + trackHeight / 2.0 + paddingTop + offsetY
        }
    }
    
    func recalculateBottomTracks() {
        let trackCount = max(0, Int(floorf(Float((viewHeight - paddingTop - paddingBottom) / trackHeight))))
        let offsetY = max(0, (viewHeight - CGFloat(trackCount) * trackHeight) / 2.0)
        let diffFloatingTrackCount = trackCount - bottomTracks.count
        if diffFloatingTrackCount > 0 {
            for _ in 0..<diffFloatingTrackCount {
                bottomTracks.insert(DanmakuVerticalTrack(view: self), at: 0)
            }
        } else if diffFloatingTrackCount < 0 {
            for i in 0..<min(bottomTracks.count, abs(diffFloatingTrackCount)) {
                bottomTracks[i].stop()
            }
            bottomTracks.removeFirst(Int(abs(diffFloatingTrackCount)))
        }
        for i in (0..<bottomTracks.count).reversed() {
            var track = bottomTracks[i]
            track.stopClosure = { [weak self] (cell) in
                guard let strongSelf = self else { return }
                strongSelf.cellPlayingStop(cell)
            }
            let index = bottomTracks.count - i - 1
            track.index = UInt(index)
            track.positionY = bounds.height - CGFloat(index) * trackHeight - trackHeight / 2.0 - paddingTop - offsetY
        }
    }
    
    func findLeastNumberDanmakuTrack(for danmaku: DanmakuCellModel) -> DanmakuTrack {
        func findLeastNumberDanmaku(from tracks: [DanmakuTrack]) -> DanmakuTrack {
            //Find a track with the minimum danmaku number
            var index = 0
            var value = Int.max
            for i in 0..<tracks.count {
                let track = tracks[i]
                if track.danmakuCount < value {
                    value = track.danmakuCount
                    index = i
                }
            }
            return tracks[index]
        }
        switch danmaku.type {
        case .floating:
            return findLeastNumberDanmaku(from: floatingTracks)
        case .top:
            return findLeastNumberDanmaku(from: topTracks)
        case .bottom:
            return findLeastNumberDanmaku(from: bottomTracks)
        }
    }
    
    func findSuitableTrack(for danmaku: DanmakuCellModel) -> DanmakuTrack? {
        switch danmaku.type {
        case .floating:
            guard let track = floatingTracks.first(where: { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) else {
                return nil
            }
            return track
        case .top:
            guard let track = topTracks.first(where: { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) else {
                return nil
            }
            return track
        case .bottom:
            guard let track = bottomTracks.last(where: { (t) -> Bool in
                return t.canShoot(danmaku: danmaku)
            }) else {
                return nil
            }
            return track
        }
    }
    
    func findSuitableSyncTrack(for danmaku: DanmakuCellModel, at progress: Float) -> DanmakuTrack? {
        switch danmaku.type {
        case .floating:
            guard let track = floatingTracks.first(where: { (t) -> Bool in
                return t.canSync(danmaku, at: progress)
            }) else {
                return nil
            }
            return track
        case .top:
            guard let track = topTracks.first(where: { (t) -> Bool in
                return t.canSync(danmaku, at: progress)
            }) else {
                return nil
            }
            return track
        case .bottom:
            guard let track = bottomTracks.last(where: { (t) -> Bool in
                return t.canSync(danmaku, at: progress)
            }) else {
                return nil
            }
            return track
        }
    }
    
    func obtainCell(with danmaku: DanmakuCellModel) -> DanmakuCell? {
        var cell: DanmakuCell?
        if enableCellReusable {
            cell = cellFromPool(danmaku)
        }
        
        let frame = CGRect(x: bounds.width, y: 0, width: danmaku.size.width, height: danmaku.size.height)
        if cell == nil {
            guard let cls = NSClassFromString(NSStringFromClass(danmaku.cellClass)) as? DanmakuCell.Type else {
                assert(false, "Launched Danmaku must inherit from DanmakuCell!")
                return nil
            }
            cell = cls.init(frame: frame)
            cell?.model = danmaku
            #if os(iOS) || os(tvOS)
            let tap = UITapGestureRecognizer(target: self, action: #selector(danmakuDidTap(_:)))
            cell?.addGestureRecognizer(tap)
            #elseif os(macOS)
            let click = NSClickGestureRecognizer(target: self, action: #selector(danmakuDidClick(_:)))
            cell?.addGestureRecognizer(click)
            #endif
        } else {
            cell?.frame = frame
            cell?.model = danmaku
            delegate?.danmakuView(self, dequeueReusable: cell!)
        }
        return cell
    }
    
    func cellFromPool(_ danmaku: DanmakuCellModel) -> DanmakuCell? {
        var cells = danmakuPool[NSStringFromClass(danmaku.cellClass)]
        if cells == nil {
            cells = []
        }
        let cell = (cells?.count ?? 0) > 0 ? cells?.removeFirst() : nil
        danmakuPool[NSStringFromClass(danmaku.cellClass)] = cells
        return cell
    }
    
    func appendCellToPool(_ cell: DanmakuCell) {
        guard let cs = cell.model?.cellClass else {
            cell.removeFromSuperview()
            return
        }
        var array = danmakuPool[NSStringFromClass(cs)]
        if array == nil {
            array = []
        }
        array?.append(cell)
        danmakuPool[NSStringFromClass(cs)] = array
    }
    
    func cellPlayingStop(_ cell: DanmakuCell) {
        delegate?.danmakuView(self, didEndDisplaying: cell)
        if enableCellReusable {
            self.appendCellToPool(cell)
        } else {
            cell.removeFromSuperview()
        }
    }
    
    #if os(iOS) || os(tvOS)
    @objc
    func danmakuDidTap(_ tap: UITapGestureRecognizer) {
        guard let view = tap.view as? DanmakuCell else { return }
        delegate?.danmakuView(self, didTapped: view)
    }
    #elseif os(macOS)
    @objc
    func danmakuDidClick(_ click: NSClickGestureRecognizer) {
        guard let view = click.view as? DanmakuCell else { return }
        delegate?.danmakuView(self, didClicked: view)
        delegate?.danmakuView(self, didTapped: view) // 同时调用 tapped 以保持兼容性
    }
    #endif
    
}
